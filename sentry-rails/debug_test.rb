#!/usr/bin/env ruby

require "bundler/setup"

# Set Rails version to match what we're testing
ENV["RAILS_VERSION"] = "7.0"

require "sentry-ruby"
require "sentry/test_helper"

# Load Rails and the test app
require_relative "spec/spec_helper"

def debug_structured_logging
  puts "=== Debugging Structured Logging ==="

  # Set up the app like in the tests
  make_basic_app do |config|
    puts "Setting up Sentry config..."
    config.enable_logs = true
    config.rails.structured_logging.enabled = true
    config.rails.structured_logging.attach_to = [:active_record]
    puts "Config enable_logs: #{config.enable_logs}"
    puts "Config structured_logging enabled: #{config.rails.structured_logging.enabled}"
    puts "Config attach_to: #{config.rails.structured_logging.attach_to}"
  end

  puts "\n=== After App Initialization ==="
  puts "Sentry initialized?: #{Sentry.initialized?}"
  puts "Sentry configuration enable_logs: #{Sentry.configuration.enable_logs}"
  puts "Rails structured logging enabled: #{Sentry.configuration.rails.structured_logging.enabled}"

  client = Sentry.get_current_client
  puts "Client: #{client.class}"
  puts "Client log_event_buffer: #{client.log_event_buffer.class if client.log_event_buffer}"

  puts "\n=== Manually Attaching Structured Logging ==="
  begin
    # Try to manually attach structured logging
    Sentry::Rails::StructuredLogging.attach(Sentry.configuration.rails.structured_logging)
    puts "Structured logging attached successfully"
  rescue => e
    puts "Error attaching structured logging: #{e.message}"
    puts e.backtrace.first(3)
  end

  puts "\n=== Checking ActiveSupport::Notifications Subscribers ==="
  # Check what subscribers are registered for sql.active_record
  listeners = ActiveSupport::Notifications.notifier.listeners_for("sql.active_record")
  puts "Listeners for sql.active_record: #{listeners.size}"
  listeners.each_with_index do |listener, i|
    delegate = listener.instance_variable_get(:@delegate)
    puts "  Listener #{i}: #{delegate.class}"
  end

  puts "\n=== Testing Database Query ==="
  begin
    # Clear any existing logs
    sentry_transport.events.clear
    sentry_transport.envelopes.clear

    # Perform a database operation
    puts "Creating a Post..."
    Post.create!(title: "Test Post", content: "Test content")
    puts "Post created successfully"

    # Flush logs
    client.flush
    puts "Flushed client"

    # Check for logs
    puts "Checking for captured logs..."
    logs = sentry_logs
    puts "Captured logs count: #{logs.size}"
    logs.each_with_index do |log, i|
      puts "  Log #{i}: #{log[:body]} (level: #{log[:level]})"
    end

  rescue => e
    puts "Error testing database query: #{e.message}"
    puts e.backtrace.first(5)
  end
end

debug_structured_logging

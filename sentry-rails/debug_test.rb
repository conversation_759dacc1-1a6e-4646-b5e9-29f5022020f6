#!/usr/bin/env ruby

require "bundler/setup"
require "sentry-ruby"

def debug_sentry_setup
  puts "=== Debugging Sentry Setup ==="

  # Initialize Sentry with logs enabled
  Sentry.init do |config|
    puts "Setting up Sentry config..."
    config.dsn = "http://12345:<EMAIL>:3000/sentry/42"
    config.transport.transport_class = Sentry::DummyTransport
    config.background_worker_threads = 0
    config.enable_logs = true
    puts "Config enable_logs: #{config.enable_logs}"
  end

  puts "\n=== After Sentry Initialization ==="
  puts "Sentry initialized?: #{Sentry.initialized?}"
  puts "Sentry configuration enable_logs: #{Sentry.configuration.enable_logs}"

  client = Sentry.get_current_client
  puts "Client: #{client.class}"
  puts "Client log_event_buffer: #{client.log_event_buffer.class if client.log_event_buffer}"

  puts "\n=== Testing Logger ==="
  begin
    logger = Sentry.logger
    puts "Sentry.logger: #{logger.class}"
    puts "Logger responds to info?: #{logger.respond_to?(:info)}"

    # Test logging
    logger.info("Test message", test_attr: "test_value")
    puts "Logged test message successfully"

    # Flush and check
    client.flush
    puts "Flushed client"

    # Check for logs
    transport = client.transport
    puts "Transport: #{transport.class}"
    if transport.respond_to?(:envelopes)
      puts "Envelopes count: #{transport.envelopes.size}"
      transport.envelopes.each_with_index do |envelope, i|
        puts "Envelope #{i}: #{envelope.items.size} items"
        envelope.items.each_with_index do |item, j|
          puts "  Item #{j}: type=#{item.headers[:type]}"
        end
      end
    end

  rescue => e
    puts "Error testing logger: #{e.message}"
    puts e.backtrace.first(5)
  end
end

debug_sentry_setup
